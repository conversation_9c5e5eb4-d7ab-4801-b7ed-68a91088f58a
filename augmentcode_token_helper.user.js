// ==UserScript==
// @name         AugmentCode Token Helper
// @namespace    http://tampermonkey.net/
// @version      0.1
// @description  帮助获取AugmentCode的授权码和Token
// <AUTHOR>
// @match        https://auth.augmentcode.com/*
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// ==/UserScript==

(function() {
    'use strict';

    // PKCE相关函数
    function base64URLEncode(buffer) {
        return btoa(String.fromCharCode.apply(null, new Uint8Array(buffer)))
            .replace(/\+/g, '-')
            .replace(/\//g, '_')
            .replace(/=/g, '');
    }

    async function sha256Hash(input) {
        const encoder = new TextEncoder();
        const data = encoder.encode(input);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        return hashBuffer;
    }

    async function createOAuthState() {
        const codeVerifierArray = new Uint8Array(32);
        crypto.getRandomValues(codeVerifierArray);
        const codeVerifier = base64URLEncode(codeVerifierArray.buffer);
        
        const codeChallenge = base64URLEncode(await sha256Hash(codeVerifier));
        
        const stateArray = new Uint8Array(8);
        crypto.getRandomValues(stateArray);
        const state = base64URLEncode(stateArray.buffer);

        const oauthState = {
            codeVerifier,
            codeChallenge,
            state,
            creationTime: Date.now()
        };

        GM_setValue('oauthState', JSON.stringify(oauthState));
        return oauthState;
    }

    // 获取token函数
    async function getAccessToken(tenant_url, codeVerifier, code) {
        return new Promise((resolve, reject) => {
            const clientID = "v";
            const data = {
                grant_type: "authorization_code",
                client_id: clientID,
                code_verifier: codeVerifier,
                redirect_uri: "",
                code: code
            };
            
            GM_xmlhttpRequest({
                method: "POST",
                url: `${tenant_url}token`,
                data: JSON.stringify(data),
                headers: {
                    "Content-Type": "application/json"
                },
                onload: function(response) {
                    try {
                        const json = JSON.parse(response.responseText);
                        const token = json.access_token;
                        resolve(token);
                    } catch (error) {
                        reject(error);
                    }
                },
                onerror: function(error) {
                    reject(error);
                }
            });
        });
    }

    // 创建UI
    function createUI() {
        const container = document.createElement('div');
        container.style.position = 'fixed';
        container.style.top = '10px';
        container.style.right = '10px';
        container.style.padding = '10px';
        container.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
        container.style.border = '1px solid #ccc';
        container.style.borderRadius = '5px';
        container.style.zIndex = '10000';
        container.style.width = '300px';
        container.style.maxHeight = '80vh';
        container.style.overflowY = 'auto';
        container.style.boxShadow = '0 0 10px rgba(0,0,0,0.2)';

        const title = document.createElement('h3');
        title.textContent = 'AugmentCode Token Helper';
        title.style.margin = '0 0 10px 0';
        container.appendChild(title);

        // 如果是授权页面，添加生成授权链接的按钮
        if (window.location.pathname.includes('/authorize')) {
            const generateBtn = document.createElement('button');
            generateBtn.textContent = '生成PKCE参数';
            generateBtn.style.margin = '5px';
            generateBtn.style.padding = '5px 10px';
            container.appendChild(generateBtn);

            const stateInfo = document.createElement('div');
            stateInfo.style.margin = '10px 0';
            stateInfo.style.wordBreak = 'break-all';
            container.appendChild(stateInfo);

            generateBtn.addEventListener('click', async () => {
                const oauthState = await createOAuthState();
                stateInfo.innerHTML = `
                    <strong>Code Verifier:</strong> <span id="codeVerifier">${oauthState.codeVerifier}</span>
                    <button id="copyVerifier" style="margin-left:5px">复制</button><br>
                    <strong>Code Challenge:</strong> ${oauthState.codeChallenge}<br>
                    <strong>State:</strong> ${oauthState.state}
                `;
                
                document.getElementById('copyVerifier').addEventListener('click', () => {
                    navigator.clipboard.writeText(oauthState.codeVerifier);
                    alert('已复制Code Verifier!');
                });
            });
        }

        // 如果是回调页面，添加获取token的功能
        if (window.location.search.includes('code=')) {
            const codeInfo = document.createElement('div');
            codeInfo.style.margin = '10px 0';
            codeInfo.style.wordBreak = 'break-all';
            container.appendChild(codeInfo);
            
            // 从URL中提取授权码
            const urlParams = new URLSearchParams(window.location.search);
            const code = urlParams.get('code');
            const state = urlParams.get('state');
            
            if (code) {
                codeInfo.innerHTML = `
                    <strong>授权码:</strong> <span id="authCode">${code}</span>
                    <button id="copyCode" style="margin-left:5px">复制</button><br>
                    <strong>State:</strong> ${state}<br>
                `;
                
                document.getElementById('copyCode').addEventListener('click', () => {
                    navigator.clipboard.writeText(code);
                    alert('已复制授权码!');
                });
                
                // 获取token按钮
                const getTokenBtn = document.createElement('button');
                getTokenBtn.textContent = '获取Token';
                getTokenBtn.style.margin = '5px';
                getTokenBtn.style.padding = '5px 10px';
                container.appendChild(getTokenBtn);
                
                const tokenInfo = document.createElement('div');
                tokenInfo.style.margin = '10px 0';
                tokenInfo.style.wordBreak = 'break-all';
                container.appendChild(tokenInfo);
                
                getTokenBtn.addEventListener('click', async () => {
                    try {
                        const savedOAuthState = JSON.parse(GM_getValue('oauthState', '{}'));
                        if (!savedOAuthState.codeVerifier) {
                            tokenInfo.innerHTML = '<span style="color:red">错误: 未找到保存的Code Verifier，请先在授权页面生成PKCE参数</span>';
                            return;
                        }
                        
                        if (savedOAuthState.state !== state) {
                            tokenInfo.innerHTML = '<span style="color:red">错误: State不匹配，可能存在安全风险</span>';
                            return;
                        }
                        
                        tokenInfo.innerHTML = '<span style="color:blue">正在获取Token...</span>';
                        
                        // 从当前URL获取tenant_url
                        const tenant_url = window.location.origin + '/';
                        
                        const token = await getAccessToken(tenant_url, savedOAuthState.codeVerifier, code);
                        
                        tokenInfo.innerHTML = `
                            <strong>Token:</strong> <span id="accessToken">${token}</span>
                            <button id="copyToken" style="margin-left:5px">复制</button>
                        `;
                        
                        document.getElementById('copyToken').addEventListener('click', () => {
                            navigator.clipboard.writeText(token);
                            alert('已复制Token!');
                        });
                        
                        // 清除保存的状态
                        GM_deleteValue('oauthState');
                        
                    } catch (error) {
                        tokenInfo.innerHTML = `<span style="color:red">错误: ${error.message}</span>`;
                    }
                });
            }
        }

        document.body.appendChild(container);
    }

    // 页面加载完成后创建UI
    window.addEventListener('load', createUI);
})(); 