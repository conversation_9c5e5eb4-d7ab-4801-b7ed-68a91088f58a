// ==UserScript==
// @name         AugmentCode Token Helper
// @namespace    http://tampermonkey.net/
// @version      0.2
// @description  帮助获取AugmentCode的授权码和Token
// <AUTHOR>
// @match        https://auth.augmentcode.com/*
// @match        https://*.api.augmentcode.com/*
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// @grant        GM_registerMenuCommand
// @grant        GM_openInTab
// ==/UserScript==

(function() {
    'use strict';

    // PKCE相关函数
    function base64URLEncode(buffer) {
        return btoa(String.fromCharCode.apply(null, new Uint8Array(buffer)))
            .replace(/\+/g, '-')
            .replace(/\//g, '_')
            .replace(/=/g, '');
    }

    async function sha256Hash(input) {
        const encoder = new TextEncoder();
        const data = encoder.encode(input);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        return hashBuffer;
    }

    async function createOAuthState() {
        const codeVerifierArray = new Uint8Array(32);
        crypto.getRandomValues(codeVerifierArray);
        const codeVerifier = base64URLEncode(codeVerifierArray.buffer);
        
        const codeChallenge = base64URLEncode(await sha256Hash(codeVerifier));
        
        const stateArray = new Uint8Array(8);
        crypto.getRandomValues(stateArray);
        const state = base64URLEncode(stateArray.buffer);

        const oauthState = {
            codeVerifier,
            codeChallenge,
            state,
            creationTime: Date.now()
        };

        GM_setValue('oauthState', JSON.stringify(oauthState));
        return oauthState;
    }

    // 获取token函数
    async function getAccessToken(tenant_url, codeVerifier, code) {
        return new Promise((resolve, reject) => {
            const clientID = "v";
            const data = {
                grant_type: "authorization_code",
                client_id: clientID,
                code_verifier: codeVerifier,
                redirect_uri: "",
                code: code
            };
            
            GM_xmlhttpRequest({
                method: "POST",
                url: `${tenant_url}token`,
                data: JSON.stringify(data),
                headers: {
                    "Content-Type": "application/json"
                },
                onload: function(response) {
                    try {
                        const json = JSON.parse(response.responseText);
                        const token = json.access_token;
                        resolve(token);
                    } catch (error) {
                        reject(error);
                    }
                },
                onerror: function(error) {
                    reject(error);
                }
            });
        });
    }

    // 生成授权链接
    async function generateAuthorizationUrl() {
        try {
            const oauthState = await createOAuthState();
            const baseUrl = 'https://auth.augmentcode.com/authorize';
            const params = new URLSearchParams({
                client_id: 'v',
                response_type: 'code',
                redirect_uri: '',
                code_challenge: oauthState.codeChallenge,
                code_challenge_method: 'S256',
                state: oauthState.state
            });

            return `${baseUrl}?${params.toString()}`;
        } catch (error) {
            console.error('生成授权链接失败:', error);
            throw error;
        }
    }

    // 处理菜单命令：生成授权链接
    async function handleGenerateAuthLink() {
        try {
            const authUrl = await generateAuthorizationUrl();
            GM_openInTab(authUrl, true); // true表示在后台打开
            alert('授权链接已生成并在新标签页打开！\n\n请在新标签页中完成授权流程。');
        } catch (error) {
            alert(`生成授权链接失败: ${error.message}`);
        }
    }

    // 创建UI
    function createUI() {
        const container = document.createElement('div');
        container.style.position = 'fixed';
        container.style.top = '10px';
        container.style.right = '10px';
        container.style.padding = '10px';
        container.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
        container.style.border = '1px solid #ccc';
        container.style.borderRadius = '5px';
        container.style.zIndex = '10000';
        container.style.width = '300px';
        container.style.maxHeight = '80vh';
        container.style.overflowY = 'auto';
        container.style.boxShadow = '0 0 10px rgba(0,0,0,0.2)';

        const title = document.createElement('h3');
        title.textContent = 'AugmentCode Token Helper';
        title.style.margin = '0 0 10px 0';
        container.appendChild(title);

        // 显示当前保存的OAuth状态
        const statusSection = document.createElement('div');
        statusSection.style.margin = '10px 0';
        statusSection.style.padding = '8px';
        statusSection.style.backgroundColor = '#f8f9fa';
        statusSection.style.border = '1px solid #dee2e6';
        statusSection.style.borderRadius = '4px';
        statusSection.style.fontSize = '12px';

        const savedOAuthState = JSON.parse(GM_getValue('oauthState', '{}'));
        if (savedOAuthState.codeVerifier) {
            statusSection.innerHTML = `
                <strong>当前保存的OAuth状态:</strong><br>
                <strong>Code Verifier:</strong> <span style="font-family:monospace; word-break:break-all;">${savedOAuthState.codeVerifier}</span><br>
                <strong>State:</strong> ${savedOAuthState.state}<br>
                <strong>创建时间:</strong> ${new Date(savedOAuthState.creationTime).toLocaleString()}<br>
                <button onclick="navigator.clipboard.writeText('${savedOAuthState.codeVerifier}');alert('已复制Code Verifier!')" style="margin-top:5px; padding:3px 8px; font-size:11px;">复制Code Verifier</button>
                <button onclick="GM_deleteValue('oauthState');location.reload();" style="margin-top:5px; margin-left:5px; padding:3px 8px; font-size:11px; background:#dc3545; color:white; border:none;">清除状态</button>
            `;
        } else {
            statusSection.innerHTML = `
                <strong>OAuth状态:</strong> <span style="color:#dc3545;">未找到保存的状态</span><br>
                <small>请先通过菜单"生成授权链接"或在授权页面"生成PKCE参数"</small>
            `;
        }
        container.appendChild(statusSection);

        // 如果是授权页面，添加生成授权链接的按钮
        if (window.location.pathname.includes('/authorize')) {
            const generateBtn = document.createElement('button');
            generateBtn.textContent = '生成PKCE参数';
            generateBtn.style.margin = '5px';
            generateBtn.style.padding = '5px 10px';
            container.appendChild(generateBtn);

            const stateInfo = document.createElement('div');
            stateInfo.style.margin = '10px 0';
            stateInfo.style.wordBreak = 'break-all';
            container.appendChild(stateInfo);

            generateBtn.addEventListener('click', async () => {
                const oauthState = await createOAuthState();
                const authUrl = await generateAuthorizationUrl();
                stateInfo.innerHTML = `
                    <strong>Code Verifier:</strong> <span id="codeVerifier">${oauthState.codeVerifier}</span>
                    <button id="copyVerifier" style="margin-left:5px">复制</button><br>
                    <strong>Code Challenge:</strong> ${oauthState.codeChallenge}<br>
                    <strong>State:</strong> ${oauthState.state}<br><br>
                    <strong>授权链接:</strong><br>
                    <textarea id="authUrl" style="width:100%;height:60px;font-size:12px;" readonly>${authUrl}</textarea><br>
                    <button id="copyAuthUrl" style="margin:5px">复制授权链接</button>
                    <button id="openAuthUrl" style="margin:5px">在新标签页打开</button>
                `;

                document.getElementById('copyVerifier').addEventListener('click', () => {
                    navigator.clipboard.writeText(oauthState.codeVerifier);
                    alert('已复制Code Verifier!');
                });

                document.getElementById('copyAuthUrl').addEventListener('click', () => {
                    navigator.clipboard.writeText(authUrl);
                    alert('已复制授权链接!');
                });

                document.getElementById('openAuthUrl').addEventListener('click', () => {
                    GM_openInTab(authUrl, true);
                    alert('授权链接已在新标签页打开!');
                });
            });
        }

        // 如果是回调页面，添加获取token的功能
        if (window.location.search.includes('code=')) {
            const codeInfo = document.createElement('div');
            codeInfo.style.margin = '10px 0';
            codeInfo.style.wordBreak = 'break-all';
            container.appendChild(codeInfo);
            
            // 从URL中提取授权码
            const urlParams = new URLSearchParams(window.location.search);
            const code = urlParams.get('code');
            const state = urlParams.get('state');
            
            if (code) {
                codeInfo.innerHTML = `
                    <strong>授权码:</strong> <span id="authCode">${code}</span>
                    <button id="copyCode" style="margin-left:5px">复制</button><br>
                    <strong>State:</strong> ${state}<br>
                `;
                
                document.getElementById('copyCode').addEventListener('click', () => {
                    navigator.clipboard.writeText(code);
                    alert('已复制授权码!');
                });
                
                // 获取token按钮
                const getTokenBtn = document.createElement('button');
                getTokenBtn.textContent = '获取Token';
                getTokenBtn.style.margin = '5px';
                getTokenBtn.style.padding = '5px 10px';
                container.appendChild(getTokenBtn);
                
                const tokenInfo = document.createElement('div');
                tokenInfo.style.margin = '10px 0';
                tokenInfo.style.wordBreak = 'break-all';
                container.appendChild(tokenInfo);
                
                getTokenBtn.addEventListener('click', async () => {
                    try {
                        const savedOAuthState = JSON.parse(GM_getValue('oauthState', '{}'));
                        if (!savedOAuthState.codeVerifier) {
                            tokenInfo.innerHTML = '<span style="color:red">错误: 未找到保存的Code Verifier，请先在授权页面生成PKCE参数</span>';
                            return;
                        }
                        
                        if (savedOAuthState.state !== state) {
                            tokenInfo.innerHTML = '<span style="color:red">错误: State不匹配，可能存在安全风险</span>';
                            return;
                        }
                        
                        tokenInfo.innerHTML = '<span style="color:blue">正在获取Token...</span>';
                        
                        // 从当前URL获取tenant_url
                        const tenant_url = window.location.origin + '/';
                        
                        const token = await getAccessToken(tenant_url, savedOAuthState.codeVerifier, code);
                        
                        tokenInfo.innerHTML = `
                            <strong>Token:</strong> <span id="accessToken">${token}</span>
                            <button id="copyToken" style="margin-left:5px">复制</button>
                        `;
                        
                        document.getElementById('copyToken').addEventListener('click', () => {
                            navigator.clipboard.writeText(token);
                            alert('已复制Token!');
                        });
                        
                        // 清除保存的状态
                        GM_deleteValue('oauthState');
                        
                    } catch (error) {
                        tokenInfo.innerHTML = `<span style="color:red">错误: ${error.message}</span>`;
                    }
                });
            }
        }

        // 添加简单的Token获取按钮
        const quickTokenBtn = document.createElement('button');
        quickTokenBtn.textContent = '快速获取Token';
        quickTokenBtn.style.margin = '10px 5px';
        quickTokenBtn.style.padding = '8px 15px';
        quickTokenBtn.style.backgroundColor = '#28a745';
        quickTokenBtn.style.color = 'white';
        quickTokenBtn.style.border = 'none';
        quickTokenBtn.style.borderRadius = '4px';
        quickTokenBtn.style.cursor = 'pointer';
        container.appendChild(quickTokenBtn);

        const quickResult = document.createElement('div');
        quickResult.style.margin = '10px 0';
        quickResult.style.wordBreak = 'break-all';
        container.appendChild(quickResult);

        quickTokenBtn.addEventListener('click', async () => {
            const code = '_f55ca2c7c6d4f238a85a2eafd4c36bfc';
            const state = 'jxvFMyOuM44';
            const tenantUrl = 'https://d13.api.augmentcode.com/';

            try {
                quickResult.innerHTML = '<span style="color:blue">正在获取Token...</span>';

                const savedOAuthState = JSON.parse(GM_getValue('oauthState', '{}'));
                console.log('保存的OAuth状态:', savedOAuthState);

                if (savedOAuthState.codeVerifier) {
                    // 使用GM_xmlhttpRequest避免CORS问题
                    const token = await getAccessToken(tenantUrl, savedOAuthState.codeVerifier, code);

                    quickResult.innerHTML = `
                        <strong>Token:</strong><br>
                        <textarea readonly style="width:100%;height:80px;font-size:11px;">${token}</textarea><br>
                        <button onclick="navigator.clipboard.writeText('${token}');alert('已复制!')">复制Token</button>
                    `;

                    GM_deleteValue('oauthState');
                } else {
                    // 如果没有Code Verifier，显示详细信息和手动输入选项
                    quickResult.innerHTML = `
                        <div style="color:red; margin-bottom:10px;">未找到Code Verifier</div>
                        <div style="font-size:12px; margin-bottom:10px;">
                            请先通过脚本菜单"生成授权链接"重新授权，或手动输入Code Verifier：
                        </div>
                        <input type="text" id="manualCodeVerifier" placeholder="输入Code Verifier" style="width:100%; margin-bottom:5px; padding:4px;">
                        <button id="manualGetToken" style="padding:5px 10px;">使用手动Code Verifier获取Token</button>
                    `;

                    document.getElementById('manualGetToken').addEventListener('click', async () => {
                        const manualCodeVerifier = document.getElementById('manualCodeVerifier').value;
                        if (!manualCodeVerifier) {
                            alert('请输入Code Verifier');
                            return;
                        }

                        try {
                            quickResult.innerHTML = '<span style="color:blue">正在获取Token...</span>';
                            const token = await getAccessToken(tenantUrl, manualCodeVerifier, code);

                            quickResult.innerHTML = `
                                <strong>Token:</strong><br>
                                <textarea readonly style="width:100%;height:80px;font-size:11px;">${token}</textarea><br>
                                <button onclick="navigator.clipboard.writeText('${token}');alert('已复制!')">复制Token</button>
                            `;
                        } catch (error) {
                            quickResult.innerHTML = `<span style="color:red">错误: ${error.message}</span>`;
                        }
                    });
                }

            } catch (error) {
                quickResult.innerHTML = `<span style="color:red">错误: ${error.message}</span>`;
            }
        });

        document.body.appendChild(container);
    }

    // 注册菜单命令
    GM_registerMenuCommand('生成授权链接', handleGenerateAuthLink);

    // 页面加载完成后创建UI
    window.addEventListener('load', createUI);
})();
